"use client";

import * as React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useProductForm } from "@/lib/contexts/ProductFormContext";
import {
  BALE_MATERIAL_OPTIONS,
  PACKAGING_TYPE_OPTIONS,
  GRADE_QUALITY_OPTIONS,
  SORTING_METHOD_OPTIONS,
  ORIGIN_OPTIONS
} from "@/lib/types/products";

// Form validation schema - exact copy from SpecificationsForm pattern
const baleSpecificationsSchema = z.object({
  material: z.string().optional(),
  origin: z.string().optional(),
  packagingType: z.string().optional(),
  gradeQuality: z.string().optional(),
  sortingMethod: z.string().optional(),
  storageConditions: z.string().optional(),
  certifications: z.string().optional(),
  notes: z.string().optional(),
});

type BaleSpecsFormData = z.infer<typeof baleSpecificationsSchema>;

interface BaleSpecificationsFormProps {
  onNext: () => void;
  onBack: () => void;
}
export default function BaleSpecificationsForm({ onNext, onBack }: BaleSpecificationsFormProps) {
  const { specifications, updateSpecifications } = useProductForm();

  // Form setup - exact copy from SpecificationsForm pattern
  const form = useForm<BaleSpecsFormData>({
    resolver: zodResolver(baleSpecificationsSchema),
    defaultValues: {
      material: specifications?.material || "",
      origin: specifications?.origin || "",
      packagingType: specifications?.packagingType || "",
      gradeQuality: specifications?.gradeQuality || "",
      sortingMethod: specifications?.sortingMethod || "",
      storageConditions: specifications?.storageConditions || "",
      certifications: specifications?.certifications || "",
      notes: specifications?.notes || "",
    },
  });

  const { control, handleSubmit, formState: { errors } } = form;

  const onSubmit = (data: BaleSpecsFormData) => {
    updateSpecifications(data);
    onNext();
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-bold mb-6">Bale Specifications</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Material */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Material</label>
            <Controller
              name="material"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select material" />
                  </SelectTrigger>
                  <SelectContent>
                    {BALE_MATERIAL_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Origin */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Origin</label>
            <Controller
              name="origin"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select origin" />
                  </SelectTrigger>
                  <SelectContent>
                    {ORIGIN_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Packaging Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Packaging Type</label>
            <Controller
              name="packagingType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select packaging type" />
                  </SelectTrigger>
                  <SelectContent>
                    {PACKAGING_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Grade Quality */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Grade Quality</label>
            <Controller
              name="gradeQuality"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select grade quality" />
                  </SelectTrigger>
                  <SelectContent>
                    {GRADE_QUALITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Sorting Method */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Sorting Method</label>
            <Controller
              name="sortingMethod"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sorting method" />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTING_METHOD_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Sorting Method */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Sorting Method</label>
            <Controller
              name="sortingMethod"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sorting method" />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTING_METHOD_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>

        {/* Storage Conditions and Certifications */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Storage Conditions</label>
            <Controller
              name="storageConditions"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., Climate controlled, Dry storage"
                />
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Certifications</label>
            <Controller
              name="certifications"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., OEKO-TEX, GOTS, Recycled content certified"
                />
              )}
            />
          </div>
        </div>

        {/* Additional Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Additional Notes
          </label>
          <Controller
            name="notes"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Any additional information about the bale composition, special characteristics, or handling instructions..."
                rows={3}
              />
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-between pt-6 border-t">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit">
            Continue to Variations
          </Button>
        </div>
      </form>

      {/* Information Card */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Specification Guidelines</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Material composition helps buyers understand the bale contents</li>
          <li>• Origin details provide transparency about sourcing</li>
          <li>• Packaging type affects shipping and storage requirements</li>
          <li>• Grade quality indicates the overall condition and value</li>
          <li>• Sorting method helps buyers understand organization level</li>
        </ul>
      </div>
    </div>
  )
}
