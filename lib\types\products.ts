// Product types and interfaces based on the API structure

// Product type enum
export type ProductType = 'product' | 'bale';

// Base variation interface
export interface BaseVariation {
  quantity: number;
  price: number;
  _id?: string;
  images?: string[];
  onSale?: boolean;
  currentPrice?: number;
  id?: string;
}

// Product-specific variation
export interface ProductVariation extends BaseVariation {
  color: string;
  size: string;
  salePrice?: number;
  saleStartDate?: string;
  saleEndDate?: string;
}

// Bale-specific variation
export interface BaleVariation extends BaseVariation {
  identifier: string; // e.g., "BALE-001"
}

// Base specifications interface
export interface BaseSpecifications {
  [key: string]: any; // Allow additional dynamic specifications
}

// Product-specific specifications
export interface ProductSpecifications extends BaseSpecifications {
  mainMaterial?: string;
  dressStyle?: string;
  pantType?: string;
  skirtType?: string;
  mensPantSize?: string;
  fitType?: 'Slim' | 'Regular' | 'Loose' | 'Oversized' | 'Tailored' | 'Skinny' | 'Straight' | 'Relaxed';
  pattern?: string;
  closure?: string;
  neckline?: string;
  sleeveLength?: string;
  waistline?: string;
  hemline?: string;
}

// Bale-specific specifications (can be extended as needed)
export interface BaleSpecifications extends BaseSpecifications {
  // Bales can have specifications too, but different from products
  material?: string;
  origin?: string;
  packagingType?: string;
}

export interface ProductSEO {
  keywords: string[];
}

// Base product/bale interface
export interface BaseProduct {
  _id: string;
  type: ProductType;
  name: string;
  description: string;
  highlights: string[];
  basePrice: number;
  images: string[];
  tags: string[];
  creator: string;
  sold: number;
  ratingsAverage: number;
  ratingsQuantity: number;
  status: 'pending' | 'active' | 'rejected' | 'draft';
  featured: boolean;
  seo: ProductSEO;
  createdAt: string;
  updatedAt: string;
  slug: string;
  __v: number;
  totalStock: number;
  minPrice: number;
  maxPrice: number;
  id: string;
}

// Product-specific interface
export interface Product extends BaseProduct {
  type: 'product';
  brand: string;
  gender: 'Male' | 'Female' | 'Unisex';
  category: string | { _id: string; name: string; description?: string }; // Can be populated or just ID
  relatedCategories: string[];
  specifications: ProductSpecifications;
  variations: ProductVariation[];
  availableColors: string[];
  availableSizes: string[];
}

// Bale-specific interface
export interface Bale extends BaseProduct {
  type: 'bale';
  country: string;
  totalItems: number;
  weight: number;
  condition: 'Good' | 'Fair' | 'Excellent';
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  specifications: BaleSpecifications;
  variations: BaleVariation[];
  availableIdentifiers: string[];
}

// Union type for both products and bales
export type ProductOrBale = Product | Bale;

// Base creation data interface
export interface BaseCreateData {
  type: ProductType;
  name: string;
  description: string;
  basePrice: number;
  productImages: File[];
  highlights: string[];
  tags: string[];
}

// Product creation data
export interface CreateProductData extends BaseCreateData {
  type: 'product';
  brand: string;
  category: string;
  gender: 'Male' | 'Female' | 'Unisex';
  specifications: ProductSpecifications;
  variations: ProductVariation[];
  relatedCategories?: string[];
}

// Bale creation data
export interface CreateBaleData extends BaseCreateData {
  type: 'bale';
  country: string;
  totalItems: number;
  weight: number;
  condition: 'Good' | 'Fair' | 'Excellent';
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  specifications: BaleSpecifications;
  variations: BaleVariation[];
}

// Union type for creation data
export type CreateProductOrBaleData = CreateProductData | CreateBaleData;

export interface CreateProductResponse {
  status: string;
  data: {
    product: ProductOrBale;
  };
}

// Form data interfaces for the multi-step form
export interface BaseInfoFormData {
  type: ProductType;
  name: string;
  description: string;
  basePrice: number;
  productImages: File[];
  highlights: string[];
  tags: string[];
}

export interface ProductInfoFormData extends BaseInfoFormData {
  type: 'product';
  brand: string;
  category: string;
  gender: 'Male' | 'Female' | 'Unisex';
}

export interface BaleInfoFormData extends BaseInfoFormData {
  type: 'bale';
  country: string;
  totalItems: number;
  weight: number;
  condition: 'Good' | 'Fair' | 'Excellent';
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface ProductSpecificationsFormData {
  mainMaterial?: string;
  dressStyle?: string;
  pantType?: string;
  skirtType?: string;
  mensPantSize?: string;
  fitType?: 'Slim' | 'Regular' | 'Loose' | 'Oversized' | 'Tailored' | 'Skinny' | 'Straight' | 'Relaxed';
  pattern?: string;
  closure?: string;
  neckline?: string;
  sleeveLength?: string;
  waistline?: string;
  hemline?: string;
  [key: string]: any;
}

export interface BaleSpecificationsFormData {
  material?: string;
  origin?: string;
  packagingType?: string;
  [key: string]: any;
}

export interface ProductVariationsFormData {
  variations: ProductVariation[];
  relatedCategories: string[];
}

export interface BaleVariationsFormData {
  variations: BaleVariation[];
}

// Combined form data for the entire product creation process
export interface ProductFormData {
  productInfo: ProductInfoFormData;
  specifications: ProductSpecificationsFormData;
  variations: ProductVariationsFormData;
}

// Combined form data for the entire bale creation process
export interface BaleFormData {
  baleInfo: BaleInfoFormData;
  specifications: BaleSpecificationsFormData;
  variations: BaleVariationsFormData;
}

// Product type options
export const PRODUCT_TYPE_OPTIONS = [
  { value: 'product', label: 'Product' },
  { value: 'bale', label: 'Bale' }
] as const;

// Gender options
export const GENDER_OPTIONS = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' },
  { value: 'Unisex', label: 'Unisex' }
] as const;

// Condition options for bales
export const CONDITION_OPTIONS = [
  { value: 'Good', label: 'Good' },
  { value: 'Fair', label: 'Fair' },
  { value: 'Excellent', label: 'Excellent' }
] as const;

// Bale specification options
export const BALE_MATERIAL_OPTIONS = [
  { value: 'cotton', label: 'Cotton' },
  { value: 'polyester', label: 'Polyester' },
  { value: 'mixed', label: 'Mixed Materials' },
  { value: 'denim', label: 'Denim' },
  { value: 'wool', label: 'Wool' },
  { value: 'silk', label: 'Silk' },
  { value: 'linen', label: 'Linen' },
  { value: 'synthetic', label: 'Synthetic' },
  { value: 'leather', label: 'Leather' },
  { value: 'cashmere', label: 'Cashmere' }
] as const;

export const PACKAGING_TYPE_OPTIONS = [
  { value: 'compressed', label: 'Compressed Bale' },
  { value: 'loose', label: 'Loose Packed' },
  { value: 'vacuum', label: 'Vacuum Sealed' },
  { value: 'wrapped', label: 'Plastic Wrapped' },
  { value: 'boxed', label: 'Boxed' },
  { value: 'bundled', label: 'Bundled' }
] as const;

export const GRADE_QUALITY_OPTIONS = [
  { value: 'premium', label: 'Premium Grade' },
  { value: 'standard', label: 'Standard Grade' },
  { value: 'economy', label: 'Economy Grade' },
  { value: 'mixed', label: 'Mixed Grade' },
  { value: 'vintage', label: 'Vintage Grade' }
] as const;

export const SORTING_METHOD_OPTIONS = [
  { value: 'unsorted', label: 'Unsorted' },
  { value: 'by-type', label: 'Sorted by Type' },
  { value: 'by-size', label: 'Sorted by Size' },
  { value: 'by-brand', label: 'Sorted by Brand' },
  { value: 'by-season', label: 'Sorted by Season' },
  { value: 'by-color', label: 'Sorted by Color' }
] as const;

export const ORIGIN_OPTIONS = [
  { value: 'uk-charity', label: 'UK Charity Shops' },
  { value: 'us-retail', label: 'US Retail Returns' },
  { value: 'eu-wholesale', label: 'EU Wholesale' },
  { value: 'factory-surplus', label: 'Factory Surplus' },
  { value: 'overstock', label: 'Overstock' },
  { value: 'liquidation', label: 'Liquidation' }
] as const;

// Fit type options
export const FIT_TYPE_OPTIONS = [
  { value: 'Slim', label: 'Slim' },
  { value: 'Regular', label: 'Regular' },
  { value: 'Loose', label: 'Loose' },
  { value: 'Oversized', label: 'Oversized' },
  { value: 'Tailored', label: 'Tailored' },
  { value: 'Skinny', label: 'Skinny' },
  { value: 'Straight', label: 'Straight' },
  { value: 'Relaxed', label: 'Relaxed' }
] as const;

// Main material options
export const MAIN_MATERIAL_OPTIONS = [
  { value: 'Cotton', label: 'Cotton' },
  { value: 'Polyester', label: 'Polyester' },
  { value: 'Denim', label: 'Denim' },
  { value: 'Silk', label: 'Silk' },
  { value: 'Wool', label: 'Wool' },
  { value: 'Linen', label: 'Linen' },
  { value: 'Leather', label: 'Leather' },
  { value: 'Synthetic', label: 'Synthetic' },
  { value: 'Blend', label: 'Blend' },
  { value: 'Other', label: 'Other' }
] as const;

// Dress style options
export const DRESS_STYLE_OPTIONS = [
  { value: 'A-Line', label: 'A-Line' },
  { value: 'Bodycon', label: 'Bodycon' },
  { value: 'Maxi', label: 'Maxi' },
  { value: 'Mini', label: 'Mini' },
  { value: 'Midi', label: 'Midi' },
  { value: 'Shift', label: 'Shift' },
  { value: 'Wrap', label: 'Wrap' },
  { value: 'Sheath', label: 'Sheath' },
  { value: 'Fit and Flare', label: 'Fit and Flare' },
  { value: 'Empire', label: 'Empire' }
] as const;

// Pant type options
export const PANT_TYPE_OPTIONS = [
  { value: 'Jeans', label: 'Jeans' },
  { value: 'Chinos', label: 'Chinos' },
  { value: 'Dress Pants', label: 'Dress Pants' },
  { value: 'Cargo', label: 'Cargo' },
  { value: 'Joggers', label: 'Joggers' },
  { value: 'Leggings', label: 'Leggings' },
  { value: 'Shorts', label: 'Shorts' },
  { value: 'Sweatpants', label: 'Sweatpants' },
  { value: 'Trousers', label: 'Trousers' }
] as const;

// Skirt type options
export const SKIRT_TYPE_OPTIONS = [
  { value: 'A-Line', label: 'A-Line' },
  { value: 'Pencil', label: 'Pencil' },
  { value: 'Pleated', label: 'Pleated' },
  { value: 'Mini', label: 'Mini' },
  { value: 'Midi', label: 'Midi' },
  { value: 'Maxi', label: 'Maxi' },
  { value: 'Circle', label: 'Circle' },
  { value: 'Wrap', label: 'Wrap' }
] as const;

// Pattern options
export const PATTERN_OPTIONS = [
  { value: 'Solid', label: 'Solid' },
  { value: 'Striped', label: 'Striped' },
  { value: 'Polka Dot', label: 'Polka Dot' },
  { value: 'Floral', label: 'Floral' },
  { value: 'Geometric', label: 'Geometric' },
  { value: 'Animal Print', label: 'Animal Print' },
  { value: 'Plaid', label: 'Plaid' },
  { value: 'Checkered', label: 'Checkered' },
  { value: 'Abstract', label: 'Abstract' }
] as const;

// Closure options
export const CLOSURE_OPTIONS = [
  { value: 'Button', label: 'Button' },
  { value: 'Zipper', label: 'Zipper' },
  { value: 'Velcro', label: 'Velcro' },
  { value: 'Snap', label: 'Snap' },
  { value: 'Tie', label: 'Tie' },
  { value: 'Hook and Eye', label: 'Hook and Eye' },
  { value: 'Pullover', label: 'Pullover' },
  { value: 'Magnetic', label: 'Magnetic' }
] as const;

// Neckline options
export const NECKLINE_OPTIONS = [
  { value: 'Round', label: 'Round' },
  { value: 'V-Neck', label: 'V-Neck' },
  { value: 'Scoop', label: 'Scoop' },
  { value: 'High Neck', label: 'High Neck' },
  { value: 'Off Shoulder', label: 'Off Shoulder' },
  { value: 'Boat Neck', label: 'Boat Neck' },
  { value: 'Square', label: 'Square' },
  { value: 'Halter', label: 'Halter' },
  { value: 'Cowl', label: 'Cowl' }
] as const;

// Sleeve length options
export const SLEEVE_LENGTH_OPTIONS = [
  { value: 'Sleeveless', label: 'Sleeveless' },
  { value: 'Short Sleeve', label: 'Short Sleeve' },
  { value: 'Long Sleeve', label: 'Long Sleeve' },
  { value: '3/4 Sleeve', label: '3/4 Sleeve' },
  { value: 'Cap Sleeve', label: 'Cap Sleeve' },
  { value: 'Bell Sleeve', label: 'Bell Sleeve' },
  { value: 'Puff Sleeve', label: 'Puff Sleeve' }
] as const;

// Waistline options
export const WAISTLINE_OPTIONS = [
  { value: 'Natural', label: 'Natural' },
  { value: 'High Waist', label: 'High Waist' },
  { value: 'Low Waist', label: 'Low Waist' },
  { value: 'Empire', label: 'Empire' },
  { value: 'Drop Waist', label: 'Drop Waist' },
  { value: 'No Waist', label: 'No Waist' }
] as const;

// Hemline options
export const HEMLINE_OPTIONS = [
  { value: 'Above Knee', label: 'Above Knee' },
  { value: 'At Knee', label: 'At Knee' },
  { value: 'Below Knee', label: 'Below Knee' },
  { value: 'Mid Calf', label: 'Mid Calf' },
  { value: 'Ankle Length', label: 'Ankle Length' },
  { value: 'Floor Length', label: 'Floor Length' },
  { value: 'Asymmetrical', label: 'Asymmetrical' }
] as const;





// Common size options
export const SIZE_OPTIONS = [
  { value: 'XS', label: 'XS' },
  { value: 'S', label: 'S' },
  { value: 'M', label: 'M' },
  { value: 'L', label: 'L' },
  { value: 'XL', label: 'XL' },
  { value: 'XXL', label: 'XXL' },
  { value: '28', label: '28' },
  { value: '30', label: '30' },
  { value: '32', label: '32' },
  { value: '34', label: '34' },
  { value: '36', label: '36' },
  { value: '38', label: '38' },
  { value: '40', label: '40' },
  { value: '42', label: '42' }
] as const;

// Common color options
export const COLOR_OPTIONS = [
  { value: 'Black', label: 'Black' },
  { value: 'White', label: 'White' },
  { value: 'Blue', label: 'Blue' },
  { value: 'Red', label: 'Red' },
  { value: 'Green', label: 'Green' },
  { value: 'Yellow', label: 'Yellow' },
  { value: 'Pink', label: 'Pink' },
  { value: 'Purple', label: 'Purple' },
  { value: 'Orange', label: 'Orange' },
  { value: 'Brown', label: 'Brown' },
  { value: 'Gray', label: 'Gray' },
  { value: 'Navy', label: 'Navy' },
  { value: 'Beige', label: 'Beige' },
  { value: 'Khaki', label: 'Khaki' }
] as const;
