"use client"

import * as React from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { X, Upload, Plus, GripVertical } from "lucide-react"
import { useProductForm } from "@/lib/contexts/ProductFormContext"
import { CONDITION_OPTIONS, BaleInfoFormData } from "@/lib/types/products"
import { toast } from "@/hooks/use-toast"

// Form validation schema for bales
const baleInfoSchema = z.object({
  name: z.string().min(1, "Bale name is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  country: z.string().min(1, "Country is required"),
  totalItems: z.coerce.number().min(1, "Total items must be at least 1"),
  weight: z.coerce.number().min(0.1, "Weight must be greater than 0"),
  condition: z.enum(['Good', 'Fair', 'Excellent'], {
    required_error: "Please select a condition",
  }),
  dimensions: z.object({
    length: z.coerce.number().min(0, "Length must be positive").optional(),
    width: z.coerce.number().min(0, "Width must be positive").optional(),
    height: z.coerce.number().min(0, "Height must be positive").optional(),
  }).optional(),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
})

type BaleInfoFormData = z.infer<typeof baleInfoSchema>

interface BaleInfoFormProps {
  onNext: () => void;
  onBack?: () => void;
}

export default function BaleInfoForm({ onNext, onBack }: BaleInfoFormProps) {
  const { productInfo, updateProductInfo } = useProductForm()
  
  // Local state for UI
  const [productImages, setProductImages] = React.useState<File[]>([])
  const [imagePreviewUrls, setImagePreviewUrls] = React.useState<string[]>([])
  const [newHighlight, setNewHighlight] = React.useState("")
  const [newTag, setNewTag] = React.useState("")
  
  // Cast productInfo to BaleInfoFormData for type safety
  const baleInfo = productInfo as BaleInfoFormData
  
  // Form setup
  const form = useForm<BaleInfoFormData>({
    resolver: zodResolver(baleInfoSchema),
    defaultValues: {
      name: baleInfo.name || "",
      description: baleInfo.description || "",
      basePrice: baleInfo.basePrice || 0,
      country: baleInfo.country || "",
      totalItems: baleInfo.totalItems || 1,
      weight: baleInfo.weight || 0,
      condition: baleInfo.condition || "Good",
      dimensions: baleInfo.dimensions || { length: 0, width: 0, height: 0 },
      highlights: baleInfo.highlights || [],
      tags: baleInfo.tags || [],
    },
  })

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form

  // Initialize image previews
  React.useEffect(() => {
    if (baleInfo.productImages && baleInfo.productImages.length > 0) {
      setProductImages(baleInfo.productImages)
      const previews = baleInfo.productImages.map(file => URL.createObjectURL(file))
      setImagePreviewUrls(previews)
    }
  }, [baleInfo.productImages])

  // Handle image uploads
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    const remainingSlots = 5 - productImages.length
    const filesToAdd = files.slice(0, remainingSlots)

    if (filesToAdd.length < files.length) {
      toast({
        variant: "destructive",
        title: "Too many images",
        description: "You can only upload up to 5 images total.",
      })
    }

    const newImages = [...productImages, ...filesToAdd]
    setProductImages(newImages)

    // Create preview URLs for new files
    const newPreviews = filesToAdd.map(file => URL.createObjectURL(file))
    setImagePreviewUrls(prev => [...prev, ...newPreviews])
  }

  // Remove image
  const removeImage = (index: number) => {
    const newImages = productImages.filter((_, i) => i !== index)
    const newPreviews = imagePreviewUrls.filter((_, i) => i !== index)
    
    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(imagePreviewUrls[index])
    
    setProductImages(newImages)
    setImagePreviewUrls(newPreviews)
  }

  // Add highlight
  const addHighlight = () => {
    if (newHighlight.trim()) {
      const currentHighlights = watch("highlights")
      setValue("highlights", [...currentHighlights, newHighlight.trim()])
      setNewHighlight("")
    }
  }

  // Remove highlight
  const removeHighlight = (index: number) => {
    const currentHighlights = watch("highlights")
    setValue("highlights", currentHighlights.filter((_, i) => i !== index))
  }

  // Add tag
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = watch("tags")
      setValue("tags", [...currentTags, newTag.trim()])
      setNewTag("")
    }
  }

  // Remove tag
  const removeTag = (index: number) => {
    const currentTags = watch("tags")
    setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  const onSubmit = (data: BaleInfoFormData) => {
    if (productImages.length === 0) {
      toast({
        variant: "destructive",
        title: "Images Required",
        description: "Please upload at least one image.",
      })
      return
    }

    // Update context with form data including images
    const formDataToSave = {
      ...data,
      type: 'bale' as const,
      productImages,
    }

    updateProductInfo(formDataToSave)
    onNext()
  }

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="w-full p-4 rounded-lg bg-white mb-4">

        {/* Image upload section */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Bale Images <span className="text-red-500">*</span>
          </label>
          <p className="text-xs text-gray-500 mb-3">Upload up to 5 images. First image will be the main image.</p>

          {/* Image grid */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            {imagePreviewUrls.map((url, index) => (
              <div
                key={index}
                className={`relative border rounded-lg h-24 bg-gray-100 cursor-move transition-all ${
                  draggedIndex === index ? 'opacity-50 scale-95' : 'hover:shadow-md'
                }`}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
              >
                <img src={url} alt={`Bale ${index + 1}`} className="w-full h-full object-cover rounded-lg" />

                {/* Drag handle */}
                <div className="absolute top-1 left-1 bg-black bg-opacity-50 text-white rounded p-1">
                  <GripVertical className="w-3 h-3" />
                </div>

                {/* Remove button */}
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 text-xs hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                </button>

                {/* Main image indicator */}
                {index === 0 && (
                  <div className="absolute bottom-1 left-1 bg-blue-500 text-white text-xs px-1 rounded">
                    Main
                  </div>
                )}

                {/* Image order indicator */}
                <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                  {index + 1}
                </div>
              </div>
            ))}

            {/* Add image button */}
            {productImages.length < 5 && (
              <label className="border-2 border-dashed border-gray-300 rounded-lg h-24 flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors">
                <Upload className="w-6 h-6 text-gray-400 mb-1" />
                <span className="text-xs text-gray-500">Add Image</span>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                />
              </label>
            )}
          </div>
        </div>

        {/* Bale name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Bale Name <span className="text-red-500">*</span>
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="e.g., Mixed Summer Clothing Bale"
                className={errors.name ? 'border-red-300' : ''}
              />
            )}
          />
          {errors.name && (
            <p className="text-xs text-red-600 mt-1">{errors.name.message}</p>
          )}
        </div>

        {/* Country and Base Price */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Country of Origin <span className="text-red-500">*</span>
            </label>
            <Controller
              name="country"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., Ghana, Nigeria, UK"
                  className={errors.country ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.country && (
              <p className="text-xs text-red-600 mt-1">{errors.country.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Base Price (GHS) <span className="text-red-500">*</span>
            </label>
            <Controller
              name="basePrice"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className={errors.basePrice ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.basePrice && (
              <p className="text-xs text-red-600 mt-1">{errors.basePrice.message}</p>
            )}
          </div>
        </div>

        {/* Quantity and Weight */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Total Items <span className="text-red-500">*</span>
            </label>
            <Controller
              name="totalItems"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  min="1"
                  placeholder="e.g., 50"
                  className={errors.totalItems ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.totalItems && (
              <p className="text-xs text-red-600 mt-1">{errors.totalItems.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Weight (kg) <span className="text-red-500">*</span>
            </label>
            <Controller
              name="weight"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="0.1"
                  min="0.1"
                  placeholder="e.g., 25.5"
                  className={errors.weight ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.weight && (
              <p className="text-xs text-red-600 mt-1">{errors.weight.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Condition <span className="text-red-500">*</span>
            </label>
            <Controller
              name="condition"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={errors.condition ? 'border-red-300' : ''}>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {CONDITION_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.condition && (
              <p className="text-xs text-red-600 mt-1">{errors.condition.message}</p>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Describe your bale - include details about the clothing types, condition, and any special features..."
                className={`min-h-[100px] ${errors.description ? 'border-red-300' : ''}`}
              />
            )}
          />
          {errors.description && (
            <p className="text-xs text-red-600 mt-1">{errors.description.message}</p>
          )}
        </div>

        {/* Dimensions (Optional) */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Dimensions (cm) - Optional
          </label>
          <div className="grid grid-cols-3 gap-4 max-w-md">
            <div>
              <Controller
                name="dimensions.length"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="Length"
                  />
                )}
              />
            </div>
            <div>
              <Controller
                name="dimensions.width"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="Width"
                  />
                )}
              />
            </div>
            <div>
              <Controller
                name="dimensions.height"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="Height"
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Highlights */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">Highlights</label>
          <p className="text-xs text-gray-500 mb-2">Add key features or selling points for your bale</p>

          <div className="flex gap-2 mb-2">
            <Input
              value={newHighlight}
              onChange={(e) => setNewHighlight(e.target.value)}
              placeholder="e.g., Premium quality, Mixed sizes"
              className="flex-1"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addHighlight()
                }
              }}
            />
            <Button type="button" onClick={addHighlight} variant="outline">
              Add
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {watch('highlights')?.map((highlight, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {highlight}
                <button
                  type="button"
                  onClick={() => removeHighlight(index)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">Tags</label>
          <p className="text-xs text-gray-500 mb-2">Add tags to help buyers find your bale</p>

          <div className="flex gap-2 mb-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="e.g., vintage, designer, casual"
              className="flex-1"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addTag()
                }
              }}
            />
            <Button type="button" onClick={addTag} variant="outline">
              Add
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {watch('tags')?.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="text-gray-600 hover:text-gray-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
        </div>

          {/* Image Previews */}
          {imagePreviewUrls.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {imagePreviewUrls.map((url, index) => (
                <div key={index} className="relative">
                  <img
                    src={url}
                    alt={`Bale image ${index + 1}`}
                    className="w-full h-24 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Highlights */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Key Highlights <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newHighlight}
              onChange={(e) => setNewHighlight(e.target.value)}
              placeholder="e.g., Mixed brands, Various sizes"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addHighlight())}
            />
            <Button type="button" onClick={addHighlight} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch("highlights").map((highlight, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {highlight}
                <button
                  type="button"
                  onClick={() => removeHighlight(index)}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.highlights && (
            <p className="text-xs text-red-600 mt-1">{errors.highlights.message}</p>
          )}
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Tags <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="e.g., wholesale, bulk, vintage"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch("tags").map((tag, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.tags && (
            <p className="text-xs text-red-600 mt-1">{errors.tags.message}</p>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-between pt-6 border-t">
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              Back
            </Button>
          )}
          <Button type="submit" className="ml-auto">
            Continue to Specifications
          </Button>
        </div>
      </form>
    </div>
  )
}
