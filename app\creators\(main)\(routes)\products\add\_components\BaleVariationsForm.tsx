"use client"

import * as React from "react"
import { use<PERSON><PERSON>, Controller, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Package } from "lucide-react"
import { useProductForm } from "@/lib/contexts/ProductFormContext"
import { BaleVariationsFormData, BaleVariation } from "@/lib/types/products"
import { useCreateProduct } from "@/lib/hooks/use-products"
import { toast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

// Form validation schema for bale variations
const baleVariationSchema = z.object({
  identifier: z.string().min(1, "Identifier is required"),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  price: z.coerce.number().min(0.01, "Price must be greater than 0"),
})

const baleVariationsSchema = z.object({
  variations: z.array(baleVariationSchema).min(1, "At least one variation is required"),
})

type BaleVariationsFormData = z.infer<typeof baleVariationsSchema>

interface BaleVariationsFormProps {
  onComplete: () => void;
  onBack: () => void;
}

export default function BaleVariationsForm({ onComplete, onBack }: BaleVariationsFormProps) {
  const { variations, updateVariations, getCompleteFormData, validateProductInfo, validateSpecifications, validateVariations } = useProductForm()
  const createProductMutation = useCreateProduct()
  const router = useRouter()
  
  // Cast variations to BaleVariationsFormData for type safety
  const baleVariations = variations as BaleVariationsFormData
  
  // Form setup
  const form = useForm<BaleVariationsFormData>({
    resolver: zodResolver(baleVariationsSchema),
    defaultValues: {
      variations: baleVariations.variations.length > 0 ? baleVariations.variations : [
        { identifier: "", quantity: 1, price: 0 }
      ],
    },
  })

  const { control, handleSubmit, watch, formState: { errors } } = form
  const { fields, append, remove } = useFieldArray({
    control,
    name: "variations",
  })

  const watchedVariations = watch("variations")

  // Calculate totals
  const totalQuantity = watchedVariations.reduce((sum, variation) => sum + (variation.quantity || 0), 0)
  const totalValue = watchedVariations.reduce((sum, variation) => sum + ((variation.quantity || 0) * (variation.price || 0)), 0)

  const addVariation = () => {
    append({ identifier: "", quantity: 1, price: 0 })
  }

  const removeVariation = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    } else {
      toast({
        variant: "destructive",
        title: "Cannot Remove",
        description: "At least one variation is required.",
      })
    }
  }

  const onSubmit = async (data: BaleVariationsFormData) => {
    // Validate all form steps
    const productInfoErrors = validateProductInfo()
    const specificationsErrors = validateSpecifications()
    const variationsErrors = validateVariations()

    if (productInfoErrors.length > 0 || specificationsErrors.length > 0 || variationsErrors.length > 0) {
      const allErrors = [...productInfoErrors, ...specificationsErrors, ...variationsErrors]
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: allErrors[0] || "Please check all form fields.",
      })
      return
    }

    // Update variations in context
    updateVariations(data)

    try {
      // Get complete form data and create the bale
      const completeData = getCompleteFormData()
      console.log('Creating bale with data:', completeData)
      
      await createProductMutation.mutateAsync(completeData)
      
      // Navigate back to products list
      router.push('/creators/products')
      onComplete()
    } catch (error) {
      console.error('Error creating bale:', error)
      // Error handling is done in the mutation
    }
  }

  return (
    <div className="w-full">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Bale Variations
        </h1>
        <p className="text-gray-600">
          Define different variations of your bale with unique identifiers, quantities, and pricing.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-6">
        {/* Variations */}
        <div className="space-y-4">
          {fields.map((field, index) => (
            <Card key={field.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Variation {index + 1}
                  </CardTitle>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeVariation(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Identifier */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Identifier <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.identifier`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="e.g., BALE-001, LOT-A"
                          className={errors.variations?.[index]?.identifier ? 'border-red-300' : ''}
                        />
                      )}
                    />
                    {errors.variations?.[index]?.identifier && (
                      <p className="text-xs text-red-600 mt-1">
                        {errors.variations[index]?.identifier?.message}
                      </p>
                    )}
                  </div>

                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Quantity <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.quantity`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min="1"
                          placeholder="1"
                          className={errors.variations?.[index]?.quantity ? 'border-red-300' : ''}
                        />
                      )}
                    />
                    {errors.variations?.[index]?.quantity && (
                      <p className="text-xs text-red-600 mt-1">
                        {errors.variations[index]?.quantity?.message}
                      </p>
                    )}
                  </div>

                  {/* Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Price (GHS) <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.price`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          step="0.01"
                          min="0.01"
                          placeholder="0.00"
                          className={errors.variations?.[index]?.price ? 'border-red-300' : ''}
                        />
                      )}
                    />
                    {errors.variations?.[index]?.price && (
                      <p className="text-xs text-red-600 mt-1">
                        {errors.variations[index]?.price?.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Variation Summary */}
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Variation Value:</span>
                    <span className="font-medium">
                      GHS {((watchedVariations[index]?.quantity || 0) * (watchedVariations[index]?.price || 0)).toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Add Variation Button */}
          <Button
            type="button"
            variant="outline"
            onClick={addVariation}
            className="w-full border-dashed"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Variation
          </Button>
        </div>

        {/* Summary Card */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-lg text-blue-900">Bale Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-blue-700">Total Variations</p>
                <p className="text-2xl font-bold text-blue-900">{fields.length}</p>
              </div>
              <div>
                <p className="text-sm text-blue-700">Total Quantity</p>
                <p className="text-2xl font-bold text-blue-900">{totalQuantity}</p>
              </div>
              <div>
                <p className="text-sm text-blue-700">Total Value</p>
                <p className="text-2xl font-bold text-blue-900">GHS {totalValue.toFixed(2)}</p>
              </div>
              <div>
                <p className="text-sm text-blue-700">Average Price</p>
                <p className="text-2xl font-bold text-blue-900">
                  GHS {totalQuantity > 0 ? (totalValue / totalQuantity).toFixed(2) : '0.00'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-between pt-6 border-t">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button 
            type="submit" 
            disabled={createProductMutation.isPending}
            className="bg-green-600 hover:bg-green-700"
          >
            {createProductMutation.isPending ? "Creating Bale..." : "Create Bale"}
          </Button>
        </div>
      </form>
    </div>
  )
}
